#!/bin/bash

# Production Deployment Script for TMS REST API
# Comprehensive deployment with cleanup, verification, and rollback capabilities
# Updated for new authentication credentials: tms-server:dr2025du

set -e  # Exit on any error

# Configuration
TARGET_SERVER="williamdu@***********"
API_URL="https://***********"
NEW_AUTH_USERNAME="tms-server"
NEW_AUTH_PASSWORD="dr2025du"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}✅ $1${NC}"
}

warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

error() {
    echo -e "${RED}❌ $1${NC}"
}

# Function to check if we're running locally or on the server
check_environment() {
    if [[ $(hostname) == *"***********"* ]] || [[ $(hostname -I 2>/dev/null | grep -q "***********") ]]; then
        echo "server"
    else
        echo "local"
    fi
}

# Function to execute commands locally or remotely
execute_cmd() {
    local cmd="$1"
    local env=$(check_environment)

    if [[ "$env" == "server" ]]; then
        # Running on server
        eval "$cmd"
    else
        # Running locally, execute on remote server
        ssh "$TARGET_SERVER" "$cmd"
    fi
}

# Function to copy files to server if running locally
copy_to_server() {
    local env=$(check_environment)

    if [[ "$env" == "local" ]]; then
        log "Copying project files to server..."
        rsync -avz --exclude 'node_modules' --exclude '.git' --exclude 'logs' \
              ./ "$TARGET_SERVER:~/tms-api/"
        success "Files copied to server"
    else
        log "Running on server, no file copy needed"
    fi
}

echo "🚀 TMS REST API Production Deployment Script"
echo "=============================================="
log "Target: $API_URL"
log "New Auth: $NEW_AUTH_USERNAME:$NEW_AUTH_PASSWORD"
echo ""

# Step 1: Pre-deployment checks
log "Step 1: Pre-deployment verification..."

# Check if secrets files exist and have correct credentials
if [[ ! -f "secrets/auth_username.txt" ]] || [[ ! -f "secrets/auth_password.txt" ]]; then
    error "Authentication secret files not found!"
    exit 1
fi

current_username=$(cat secrets/auth_username.txt)
current_password=$(cat secrets/auth_password.txt)

if [[ "$current_username" != "$NEW_AUTH_USERNAME" ]] || [[ "$current_password" != "$NEW_AUTH_PASSWORD" ]]; then
    error "Secret files don't match expected credentials!"
    echo "Expected: $NEW_AUTH_USERNAME:$NEW_AUTH_PASSWORD"
    echo "Found: $current_username:$current_password"
    exit 1
fi

success "Authentication credentials verified"

# Step 2: Copy files to server (if running locally)
copy_to_server

# Step 3: Server-side deployment
log "Step 2: Starting server-side deployment..."

execute_cmd "cd ~/tms-api"

# Step 4: Cleanup existing deployment
log "Step 3: Cleaning up existing deployment..."
execute_cmd "cd ~/tms-api && docker-compose -f docker-compose.prod.yml down --remove-orphans || true"
execute_cmd "cd ~/tms-api && docker system prune -f || true"
execute_cmd "cd ~/tms-api && docker rmi tms-api-prod-container:latest || true"

success "Cleanup completed"

# Step 5: Build production image
log "Step 4: Building production Docker image..."
execute_cmd "cd ~/tms-api && docker build -t tms-api-prod-container:latest --target production ."

success "Production image built"

# Step 6: Start production services
log "Step 5: Starting production services..."
execute_cmd "cd ~/tms-api && docker-compose -f docker-compose.prod.yml up -d"

success "Production services started"

# Step 7: Wait for services to be ready
log "Step 6: Waiting for services to initialize..."
sleep 45

# Step 8: Health checks
log "Step 7: Performing comprehensive health checks..."

# Basic connectivity check
log "Checking basic HTTPS connectivity..."
if execute_cmd "curl -k -f --connect-timeout 10 --max-time 15 $API_URL/health > /dev/null 2>&1"; then
    success "Basic connectivity check passed"
else
    error "Basic connectivity check failed"
    execute_cmd "cd ~/tms-api && docker-compose -f docker-compose.prod.yml logs api"
    exit 1
fi

# Authentication check with new credentials
log "Testing authentication with new credentials..."
AUTH_HEADER=$(echo -n "$NEW_AUTH_USERNAME:$NEW_AUTH_PASSWORD" | base64)
if execute_cmd "curl -k -f --connect-timeout 10 --max-time 15 -H 'Authorization: Basic $AUTH_HEADER' -H 'X-Correlation-ID: deploy-test-$(date +%s)' $API_URL/protected > /dev/null 2>&1"; then
    success "Authentication check passed with new credentials"
else
    error "Authentication check failed with new credentials"
    execute_cmd "cd ~/tms-api && docker-compose -f docker-compose.prod.yml logs api"
    exit 1
fi

# Container health check
log "Checking container health status..."
execute_cmd "cd ~/tms-api && docker-compose -f docker-compose.prod.yml ps"

# Step 9: Final verification
log "Step 8: Final deployment verification..."

# Test all critical endpoints
log "Testing critical API endpoints..."

# Test health endpoint (no auth required)
if execute_cmd "curl -k -s --connect-timeout 10 --max-time 15 $API_URL/health | grep -q 'status.*ok'"; then
    success "Health endpoint responding correctly"
else
    warning "Health endpoint response unexpected"
fi

# Test protected endpoint (auth required)
if execute_cmd "curl -k -s --connect-timeout 10 --max-time 15 -H 'Authorization: Basic $AUTH_HEADER' -H 'X-Correlation-ID: deploy-final-test-$(date +%s)' $API_URL/protected | grep -q 'protected endpoint'"; then
    success "Protected endpoint responding correctly"
else
    warning "Protected endpoint response unexpected"
fi

# Test quiz endpoint structure (auth required)
if execute_cmd "curl -k -s --connect-timeout 10 --max-time 15 -H 'Authorization: Basic $AUTH_HEADER' -H 'X-Correlation-ID: deploy-quiz-test-$(date +%s)' '$API_URL/quiz/f2f/paperless-marking-worked-solutions?year=2025&term=1&week=1&weekType=normal' | grep -q 'quizzes'"; then
    success "Quiz endpoint responding correctly"
else
    warning "Quiz endpoint response unexpected (may be empty, which is normal)"
fi

success "Deployment verification completed"

# Step 10: Display deployment summary
echo ""
echo "🎉 Production Deployment Completed Successfully!"
echo "=============================================="
log "API URL: $API_URL"
log "Health Check: $API_URL/health"
log "API Documentation: $API_URL/api/docs"
log "Authentication: $NEW_AUTH_USERNAME:$NEW_AUTH_PASSWORD"
echo ""

# Display running containers
log "Production containers status:"
execute_cmd "cd ~/tms-api && docker-compose -f docker-compose.prod.yml ps"

echo ""
success "Deployment completed successfully!"
echo ""
echo "🔐 Test the deployment with:"
echo "curl -k -u '$NEW_AUTH_USERNAME:$NEW_AUTH_PASSWORD' -H 'X-Correlation-ID: test-$(date +%s)' '$API_URL/protected'"
echo ""
echo "📚 Access API documentation at: $API_URL/api/docs"
echo ""

# Rollback function (for manual use if needed)
rollback() {
    error "Initiating rollback procedure..."
    execute_cmd "cd ~/tms-api && docker-compose -f docker-compose.prod.yml down"
    error "Services stopped. Manual intervention required."
    exit 1
}

# Export rollback function for manual use
export -f rollback
