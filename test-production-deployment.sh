#!/bin/bash

# TMS REST API Production Deployment Test Suite
# Target: https://172.16.2.59
# Testing SSL, Authentication, CORS, and API Endpoints

echo "=== TMS REST API Production Deployment Test Suite ==="
echo "Target: https://172.16.2.59"
echo "Testing SSL, Authentication, CORS, and API Endpoints"
echo ""

# Create base64 encoded credentials for Basic Auth
AUTH_CREDENTIALS=$(echo -n "tms-server:dr2025du" | base64)
echo "Generated Basic Auth credentials: $AUTH_CREDENTIALS"
echo ""

echo "=== TEST 1: Basic HTTPS Connectivity & SSL Certificate ==="
echo "Testing HTTPS connection and SSL certificate..."
curl -v -k --connect-timeout 10 --max-time 15 https://172.16.2.59/health 2>&1 | head -30
echo ""

echo "=== TEST 2: HTTP to HTTPS Redirect ==="
echo "Testing HTTP to HTTPS redirect..."
curl -v --connect-timeout 10 --max-time 15 -L http://172.16.2.59/health 2>&1 | grep -E "(HTTP|Location|SSL)" || echo "No redirect detected"
echo ""

echo "=== TEST 3: Health Check Endpoint (No Auth Required) ==="
echo "Testing GET /health endpoint..."
curl -s --connect-timeout 10 --max-time 15 \
  -H "X-Correlation-ID: test-$(date +%s)" \
  -H "Accept: application/json" \
  https://172.16.2.59/health | jq . 2>/dev/null || curl -s https://172.16.2.59/health
echo ""

echo "=== TEST 4: Authentication Test - Valid Credentials ==="
echo "Testing authenticated endpoint with valid credentials..."
curl -s --connect-timeout 10 --max-time 15 \
  -H "Authorization: Basic $AUTH_CREDENTIALS" \
  -H "X-Correlation-ID: test-auth-$(date +%s)" \
  -H "Accept: application/json" \
  "https://172.16.2.59/quiz/f2f/paperless-marking-worked-solutions" | jq . 2>/dev/null || curl -s -H "Authorization: Basic $AUTH_CREDENTIALS" "https://172.16.2.59/quiz/f2f/paperless-marking-worked-solutions"
echo ""

echo "=== TEST 5: Authentication Test - Invalid Credentials ==="
echo "Testing with invalid credentials (should return 401)..."
INVALID_CREDENTIALS=$(echo -n "invalid:credentials" | base64)
curl -s --connect-timeout 10 --max-time 15 \
  -w "HTTP Status: %{http_code}\n" \
  -H "Authorization: Basic $INVALID_CREDENTIALS" \
  -H "X-Correlation-ID: test-invalid-$(date +%s)" \
  -H "Accept: application/json" \
  "https://172.16.2.59/quiz/f2f/paperless-marking-worked-solutions" | jq . 2>/dev/null || echo "Response received"
echo ""

echo "=== TEST 6: CORS Headers Test ==="
echo "Testing CORS configuration..."
curl -s --connect-timeout 10 --max-time 15 \
  -H "Origin: https://172.16.2.59" \
  -H "Access-Control-Request-Method: GET" \
  -H "Access-Control-Request-Headers: Authorization,X-Correlation-ID" \
  -X OPTIONS \
  -I https://172.16.2.59/health 2>/dev/null | grep -i "access-control" || echo "No CORS headers found"
echo ""

echo "=== TEST 7: Security Headers Test ==="
echo "Testing security headers (HSTS, CSP, etc.)..."
curl -s --connect-timeout 10 --max-time 15 \
  -I https://172.16.2.59/health 2>/dev/null | grep -E "(Strict-Transport-Security|Content-Security-Policy|X-Frame-Options|X-Content-Type-Options)" || echo "Security headers check complete"
echo ""

echo "=== TEST 8: API Documentation Access ==="
echo "Testing Swagger/OpenAPI documentation..."
curl -s --connect-timeout 10 --max-time 15 \
  -w "HTTP Status: %{http_code}\n" \
  -o /dev/null \
  https://172.16.2.59/api/docs
echo ""

echo "=== TEST 9: Rate Limiting Test ==="
echo "Testing rate limiting (production limits)..."
for i in {1..3}; do
  echo "Request $i:"
  curl -s --connect-timeout 5 --max-time 10 \
    -w "HTTP Status: %{http_code}, Time: %{time_total}s\n" \
    -o /dev/null \
    https://172.16.2.59/health
done
echo ""

echo "=== TEST 10: Full API Response Structure Test ==="
echo "Testing complete API response structure..."
curl -s --connect-timeout 10 --max-time 15 \
  -H "Authorization: Basic $AUTH_CREDENTIALS" \
  -H "X-Correlation-ID: test-structure-$(date +%s)" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  "https://172.16.2.59/quiz/f2f/paperless-marking-worked-solutions?subject=Mathematics&year=2024&term=1" | jq . 2>/dev/null || echo "Raw response received"
echo ""

echo "=== Production Deployment Test Suite Complete ==="
echo "Review the results above to verify:"
echo "1. SSL/HTTPS is working correctly"
echo "2. Authentication is functioning"
echo "3. CORS headers are properly configured"
echo "4. Security headers are in place"
echo "5. API endpoints are responding correctly"
echo "6. Rate limiting is active"
