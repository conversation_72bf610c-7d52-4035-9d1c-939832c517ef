#!/bin/bash

# TMS REST API - One-Command Production Deployment
# Usage: ./deploy.sh
# 
# This script provides a simple one-command deployment solution for the TMS REST API
# with comprehensive error handling, verification, and rollback capabilities.

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}"
echo "╔══════════════════════════════════════════════════════════════╗"
echo "║                TMS REST API - Production Deployment         ║"
echo "║                                                              ║"
echo "║  Target: https://***********                                ║"
echo "║  Auth: tms-server:dr2025du                                   ║"
echo "║                                                              ║"
echo "║  This script will:                                           ║"
echo "║  1. Verify credentials and prerequisites                     ║"
echo "║  2. Deploy to production server                              ║"
echo "║  3. Perform comprehensive health checks                      ║"
echo "║  4. Verify new authentication works                          ║"
echo "╚══════════════════════════════════════════════════════════════╝"
echo -e "${NC}"
echo ""

# Check if deployment script exists
if [[ ! -f "redeploy-production.sh" ]]; then
    echo -e "${RED}❌ redeploy-production.sh not found!${NC}"
    exit 1
fi

# Check if we have the required files
echo -e "${BLUE}🔍 Pre-deployment checks...${NC}"

required_files=(
    "docker-compose.prod.yml"
    "secrets/auth_username.txt"
    "secrets/auth_password.txt"
    "nginx/nginx.conf"
    "ssl/nginx/tms-api-prod.crt"
    "ssl/nginx/tms-api-prod.key"
)

for file in "${required_files[@]}"; do
    if [[ ! -f "$file" ]]; then
        echo -e "${RED}❌ Required file missing: $file${NC}"
        exit 1
    fi
done

echo -e "${GREEN}✅ All required files present${NC}"

# Verify credentials
expected_username="tms-server"
expected_password="dr2025du"

actual_username=$(cat secrets/auth_username.txt)
actual_password=$(cat secrets/auth_password.txt)

if [[ "$actual_username" != "$expected_username" ]] || [[ "$actual_password" != "$expected_password" ]]; then
    echo -e "${RED}❌ Credentials mismatch!${NC}"
    echo "Expected: $expected_username:$expected_password"
    echo "Found: $actual_username:$actual_password"
    exit 1
fi

echo -e "${GREEN}✅ Credentials verified${NC}"
echo ""

# Confirm deployment
echo -e "${YELLOW}⚠️  You are about to deploy to PRODUCTION server williamdu@***********${NC}"
echo -e "${YELLOW}   This will stop existing services and deploy new containers.${NC}"
echo ""
read -p "Continue with deployment? (y/N): " -n 1 -r
echo ""

if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo -e "${YELLOW}Deployment cancelled.${NC}"
    exit 0
fi

echo ""
echo -e "${BLUE}🚀 Starting deployment...${NC}"
echo ""

# Execute the main deployment script
./redeploy-production.sh

# If we get here, deployment was successful
echo ""
echo -e "${GREEN}"
echo "╔══════════════════════════════════════════════════════════════╗"
echo "║                    DEPLOYMENT SUCCESSFUL!                   ║"
echo "║                                                              ║"
echo "║  🌐 API URL: https://***********                            ║"
echo "║  🏥 Health: https://***********/health                      ║"
echo "║  📚 Docs: https://***********/api/docs                      ║"
echo "║  🔐 Auth: tms-server:dr2025du                                ║"
echo "║                                                              ║"
echo "║  Test with:                                                  ║"
echo "║  curl -k -u 'tms-server:dr2025du' \\                         ║"
echo "║       https://***********/protected                         ║"
echo "╚══════════════════════════════════════════════════════════════╝"
echo -e "${NC}"
echo ""

# Offer to run post-deployment tests
echo -e "${BLUE}Would you like to run comprehensive post-deployment tests? (y/N):${NC}"
read -p "" -n 1 -r
echo ""

if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo ""
    echo -e "${BLUE}🧪 Running post-deployment tests...${NC}"
    ./test-production-deployment.sh
fi

echo ""
echo -e "${GREEN}🎉 Deployment process completed!${NC}"
