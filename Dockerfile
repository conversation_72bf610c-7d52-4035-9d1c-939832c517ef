# Base image
FROM node:22-alpine AS development

# Add metadata labels
LABEL maintainer="William <PERSON>"
LABEL project="TMS REST API"
LABEL environment="development"
LABEL version="1.0.0"
LABEL description="Task Management System REST API - Development Build by <PERSON>"

# Create app directory
WORKDIR /usr/src/app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm install

# Copy source code
COPY . .

# Build the application
RUN npm run build

# Expose the port the app runs on
EXPOSE 3000

# Command to run the application
CMD ["npm", "run", "start:dev"]

# Production build
FROM node:22-alpine AS production

# Add metadata labels for production
LABEL maintainer="William Du"
LABEL project="TMS REST API"
LABEL environment="production"
LABEL version="1.0.0"
LABEL description="Task Management System REST API - Production Build by <PERSON>"

# Set NODE_ENV
ENV NODE_ENV=production

# Install security updates and required packages
RUN apk update && apk upgrade && \
    apk add --no-cache curl && \
    rm -rf /var/cache/apk/*

# Create non-root user for security
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nestjs -u 1001

# Create app directory
WORKDIR /usr/src/app

# Copy package files
COPY package*.json ./

# Install only production dependencies
RUN npm ci --only=production && npm cache clean --force

# Copy built application from development stage
COPY --from=development /usr/src/app/dist ./dist

# Change ownership to non-root user
RUN chown -R nestjs:nodejs /usr/src/app

# Switch to non-root user
USER nestjs

# Expose the port the app runs on
EXPOSE 3000

# Add health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
    CMD curl -f http://localhost:3000/health || exit 1

# Add proper signal handling
STOPSIGNAL SIGTERM

# Command to run the application
CMD ["node", "dist/src/main"]
