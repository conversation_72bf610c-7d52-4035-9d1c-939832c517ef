# TMS REST API - Production Deployment Guide

## 🚀 One-Command Deployment

For the simplest deployment experience, use the automated deployment script:

```bash
./deploy.sh
```

This single command will:
- ✅ Verify all prerequisites and credentials
- 🧹 Clean up existing deployment
- 🔨 Build and deploy new containers
- 🏥 Perform comprehensive health checks
- 🔐 Test new authentication credentials
- 📊 Display deployment status and URLs

## 📋 Prerequisites

Before deployment, ensure you have:

1. **SSH Access**: Configured SSH key access to `william<PERSON>@***********`
2. **Updated Credentials**: New auth credentials (`tms-server:dr2025du`) in secrets files
3. **SSL Certificates**: Valid SSL certificates in `ssl/nginx/` directory
4. **Docker**: Docker and Docker Compose installed on target server

## 🔐 Authentication Update

The deployment uses new authentication credentials:
- **Username**: `tms-server`
- **Password**: `dr2025du`

These credentials are automatically verified before deployment.

## 📁 Required Files

The deployment script verifies these files exist:

```
├── docker-compose.prod.yml          # Production Docker configuration
├── secrets/
│   ├── auth_username.txt            # Contains: tms-server
│   ├── auth_password.txt            # Contains: dr2025du
│   ├── db_username.txt              # Database credentials
│   ├── db_password.txt
│   ├── db_database.txt
│   ├── minio_access_key.txt         # MinIO credentials
│   ├── minio_secret_key.txt
│   └── minio_bucket.txt
├── nginx/
│   └── nginx.conf                   # Nginx reverse proxy config
└── ssl/
    ├── nginx/
    │   ├── tms-api-prod.crt         # SSL certificate
    │   └── tms-api-prod.key         # SSL private key
    └── minio/
        ├── public-prod.crt          # MinIO SSL certificate
        └── private-prod.key         # MinIO SSL private key
```

## 🔧 Manual Deployment Steps

If you prefer manual control, follow these steps:

### 1. Pre-deployment Verification

```bash
# Verify credentials
cat secrets/auth_username.txt  # Should show: tms-server
cat secrets/auth_password.txt  # Should show: dr2025du

# Check SSL certificates
ls -la ssl/nginx/tms-api-prod.*
ls -la ssl/minio/public-prod.crt ssl/minio/private-prod.key
```

### 2. Deploy to Server

```bash
# Run the comprehensive deployment script
./redeploy-production.sh
```

### 3. Verify Deployment

```bash
# Test basic connectivity
curl -k https://***********/health

# Test authentication
curl -k -u "tms-server:dr2025du" \
     -H "X-Correlation-ID: test-$(date +%s)" \
     https://***********/protected

# Run comprehensive tests
./test-production-deployment.sh
```

## 🏥 Health Checks

The deployment includes automatic health checks for:

- **HTTPS Connectivity**: SSL certificate and basic connection
- **Authentication**: New credentials validation
- **Container Health**: All Docker containers running
- **API Endpoints**: Critical endpoints responding
- **Database**: PostgreSQL connection and health
- **Storage**: MinIO service availability

## 🔄 Rollback Procedure

If deployment fails, the script provides automatic rollback:

```bash
# Manual rollback (if needed)
ssh williamdu@*********** "cd ~/tms-api && docker-compose -f docker-compose.prod.yml down"
```

## 📊 Post-Deployment Verification

After successful deployment, verify these endpoints:

| Endpoint | Auth Required | Expected Response |
|----------|---------------|-------------------|
| `GET /health` | No | `{"status": "ok", ...}` |
| `GET /protected` | Yes | `{"message": "This is a protected endpoint..."}` |
| `GET /quiz/f2f/paperless-marking-worked-solutions` | Yes | `{"quizzes": [...]}` |
| `GET /api/docs` | No | Swagger UI |

## 🌐 Production URLs

After deployment, access the API at:

- **API Base URL**: https://***********
- **Health Check**: https://***********/health
- **API Documentation**: https://***********/api/docs
- **Protected Test**: https://***********/protected

## 🔐 Testing Authentication

Test the new authentication credentials:

```bash
# Basic test
curl -k -u "tms-server:dr2025du" https://***********/protected

# With correlation ID (recommended)
curl -k -u "tms-server:dr2025du" \
     -H "X-Correlation-ID: $(uuidgen)" \
     https://***********/protected

# Test quiz endpoint
curl -k -u "tms-server:dr2025du" \
     -H "X-Correlation-ID: $(uuidgen)" \
     "https://***********/quiz/f2f/paperless-marking-worked-solutions?year=2025&term=1&week=1&weekType=normal"
```

## 🚨 Troubleshooting

### Common Issues

1. **SSL Certificate Errors**
   - Verify certificates exist in `ssl/nginx/`
   - Check certificate validity: `openssl x509 -in ssl/nginx/tms-api-prod.crt -text -noout`

2. **Authentication Failures**
   - Verify credentials in secrets files
   - Check base64 encoding: `echo -n "tms-server:dr2025du" | base64`

3. **Container Startup Issues**
   - Check logs: `docker-compose -f docker-compose.prod.yml logs`
   - Verify Docker daemon running on server

4. **Network Connectivity**
   - Test SSH access: `ssh williamdu@***********`
   - Check firewall rules for ports 80, 443

### Log Locations

- **Application Logs**: `docker-compose -f docker-compose.prod.yml logs api`
- **Nginx Logs**: `docker-compose -f docker-compose.prod.yml logs reverse-proxy`
- **Database Logs**: `docker-compose -f docker-compose.prod.yml logs postgres`
- **MinIO Logs**: `docker-compose -f docker-compose.prod.yml logs minio`

## 📞 Support

For deployment issues:

1. Check the health endpoint: https://***********/health
2. Review container logs: `docker-compose -f docker-compose.prod.yml logs`
3. Verify all required files are present and have correct permissions
4. Ensure SSH access to the target server is working

---

**Last Updated**: Updated for new authentication credentials `tms-server:dr2025du`
